[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "blivedm"
dynamic = ["version"]
description = "Python获取bilibili直播弹幕的库，使用WebSocket协议"
readme = "README.md"
keywords = ["bilibili", "bilibili-live", "danmaku"]
requires-python = ">=3.8"
authors = [
    {name = "xfgryujk", email = "<EMAIL>"},
]
license = {file = "LICENSE"}
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Framework :: AsyncIO",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Software Development :: Libraries",
]
dependencies = [
    "aiohttp~=3.9.0",
    "Brotli~=1.1.0",
    "yarl~=1.9.3",
]

[project.urls]
Homepage = "https://github.com/xfgryujk/blivedm"
Repository = "https://github.com/xfgryujk/blivedm"
Issues = "https://github.com/xfgryujk/blivedm/issues"

[tool.pdm]
version = {source = "file", path = "blivedm/__init__.py"}
distribution = true
